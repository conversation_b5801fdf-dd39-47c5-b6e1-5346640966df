import { Link, useNavigate } from 'react-router-dom';
import { User } from '../../utils/api';

interface HeaderProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function Header({ currentUser, onLogout }: HeaderProps) {
  const navigate = useNavigate();

  const handleLogout = () => {
    onLogout();
    navigate('/');
  };

  return (
    <header className="header">
      <div className="header-content">
        <div className="header-left">
          <Link to="/" className="logo-link">
            <h1 className="business-name-header">dammyspicybeauty</h1>
          </Link>
        </div>
        
        <div className="auth-links">
          {currentUser ? (
            <>
              <Link to="/dashboard" className="auth-link">
                DASHBOARD
              </Link>
              <button className="auth-link" onClick={handleLogout}>
                LOGOUT
              </button>
            </>
          ) : (
            <>
              <Link to="/signup" className="auth-link">
                SIGN UP
              </Link>
              <Link to="/login" className="auth-link">
                LOG IN
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
