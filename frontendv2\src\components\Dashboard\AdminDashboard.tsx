import { useState, useEffect } from 'react';
import { 
  getCurrentUser, 
  getUsers, 
  getAppointments, 
  updateAppointment, 
  deleteAppointment, 
  logout,
  type User,
  type Appointment 
} from '../../utils/auth';

interface AdminDashboardProps {
  onLogout: () => void;
}

export default function AdminDashboard({ onLogout }: AdminDashboardProps) {
  const [user, setUser] = useState(getCurrentUser());
  const [users, setUsers] = useState<User[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'appointments' | 'users' | 'analytics'>('overview');
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setUsers(getUsers());
    setAppointments(getAppointments());
  };

  const handleLogout = () => {
    logout();
    onLogout();
  };

  const handleUpdateAppointmentStatus = (appointmentId: string, status: Appointment['status']) => {
    const updated = updateAppointment(appointmentId, { status });
    if (updated) {
      loadData(); // Refresh data
    }
  };

  const handleDeleteAppointment = (appointmentId: string) => {
    if (window.confirm('Are you sure you want to delete this appointment?')) {
      deleteAppointment(appointmentId);
      loadData(); // Refresh data
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Analytics calculations
  const totalRevenue = appointments
    .filter(apt => apt.status === 'completed')
    .reduce((sum, apt) => sum + apt.totalPrice, 0);

  const pendingRevenue = appointments
    .filter(apt => apt.status === 'confirmed' || apt.status === 'pending')
    .reduce((sum, apt) => sum + apt.totalPrice, 0);

  const todayAppointments = appointments.filter(apt => {
    const today = new Date().toDateString();
    return new Date(apt.date).toDateString() === today;
  });

  const upcomingAppointments = appointments.filter(apt => 
    new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
  );

  if (!user || user.role !== 'admin') {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return (
    <div className="dashboard-container admin">
      {/* Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-logo">
            <h1>dammyspicybeauty - Admin</h1>
          </div>
          <div className="dashboard-user-menu">
            <span className="welcome-text">Welcome, {user.firstName}!</span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-content">
          {/* Navigation Tabs */}
          <div className="dashboard-tabs">
            <button 
              className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
              onClick={() => setActiveTab('overview')}
            >
              📊 Overview
            </button>
            <button 
              className={`tab-button ${activeTab === 'appointments' ? 'active' : ''}`}
              onClick={() => setActiveTab('appointments')}
            >
              📅 Appointments ({appointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
              onClick={() => setActiveTab('users')}
            >
              👥 Users ({users.filter(u => u.role === 'user').length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'analytics' ? 'active' : ''}`}
              onClick={() => setActiveTab('analytics')}
            >
              📈 Analytics
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'overview' && (
              <div className="overview-section">
                <div className="stats-grid admin-stats">
                  <div className="stat-card">
                    <h3>{appointments.length}</h3>
                    <p>Total Appointments</p>
                  </div>
                  <div className="stat-card">
                    <h3>{todayAppointments.length}</h3>
                    <p>Today's Appointments</p>
                  </div>
                  <div className="stat-card">
                    <h3>{users.filter(u => u.role === 'user').length}</h3>
                    <p>Total Clients</p>
                  </div>
                  <div className="stat-card">
                    <h3>${totalRevenue.toFixed(2)}</h3>
                    <p>Total Revenue</p>
                  </div>
                </div>

                <div className="recent-activity">
                  <h3>Recent Appointments</h3>
                  <div className="appointments-list compact">
                    {upcomingAppointments.slice(0, 5).map(appointment => (
                      <div key={appointment.id} className="appointment-card compact">
                        <div className="appointment-header">
                          <h4>{appointment.customerInfo.firstName} {appointment.customerInfo.lastName}</h4>
                          <span 
                            className="status-badge"
                            style={{ backgroundColor: getStatusColor(appointment.status) }}
                          >
                            {appointment.status.toUpperCase()}
                          </span>
                        </div>
                        <div className="appointment-details">
                          <p>{appointment.serviceName}</p>
                          <p>{formatDate(appointment.date)} at {appointment.time}</p>
                          <p>${appointment.totalPrice.toFixed(2)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'appointments' && (
              <div className="appointments-section">
                <div className="section-header">
                  <h3>All Appointments</h3>
                  <div className="filters">
                    <select onChange={(e) => {
                      // Filter logic can be added here
                    }}>
                      <option value="all">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="confirmed">Confirmed</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                </div>

                <div className="appointments-list">
                  {appointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card admin">
                      <div className="appointment-header">
                        <div>
                          <h4>{appointment.customerInfo.firstName} {appointment.customerInfo.lastName}</h4>
                          <p className="service-name">{appointment.serviceName}</p>
                        </div>
                        <div className="appointment-actions">
                          <select 
                            value={appointment.status}
                            onChange={(e) => handleUpdateAppointmentStatus(appointment.id, e.target.value as Appointment['status'])}
                            className="status-select"
                          >
                            <option value="pending">Pending</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                          </select>
                          <button 
                            className="delete-button"
                            onClick={() => handleDeleteAppointment(appointment.id)}
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                      <div className="appointment-details">
                        <div className="detail-row">
                          <span><strong>Date:</strong> {formatDate(appointment.date)}</span>
                          <span><strong>Time:</strong> {appointment.time}</span>
                          <span><strong>Total:</strong> ${appointment.totalPrice.toFixed(2)}</span>
                        </div>
                        <div className="detail-row">
                          <span><strong>Email:</strong> {appointment.customerInfo.email}</span>
                          <span><strong>Phone:</strong> {appointment.customerInfo.phone}</span>
                          <span><strong>Deposit:</strong> {appointment.depositPaid ? '✅ Paid' : '❌ Pending'}</span>
                        </div>
                        {appointment.addOns.length > 0 && (
                          <div className="add-ons">
                            <strong>Add-ons:</strong> {appointment.addOns.map(addon => addon.name).join(', ')}
                          </div>
                        )}
                        {appointment.notes && (
                          <div className="appointment-notes">
                            <strong>Notes:</strong> {appointment.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'users' && (
              <div className="users-section">
                <h3>Client Management</h3>
                <div className="users-list">
                  {users.filter(u => u.role === 'user').map(user => {
                    const userAppointments = appointments.filter(apt => apt.userId === user.id);
                    const totalSpent = userAppointments.reduce((sum, apt) => sum + apt.totalPrice, 0);
                    
                    return (
                      <div key={user.id} className="user-card">
                        <div className="user-header">
                          <h4>{user.firstName} {user.lastName}</h4>
                          <span className="user-stats">{userAppointments.length} appointments</span>
                        </div>
                        <div className="user-details">
                          <p><strong>Email:</strong> {user.email}</p>
                          <p><strong>Phone:</strong> {user.phone}</p>
                          <p><strong>Member Since:</strong> {new Date(user.createdAt).toLocaleDateString()}</p>
                          <p><strong>Total Spent:</strong> ${totalSpent.toFixed(2)}</p>
                          <p><strong>Last Login:</strong> {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'analytics' && (
              <div className="analytics-section">
                <h3>Business Analytics</h3>
                <div className="analytics-grid">
                  <div className="analytics-card">
                    <h4>Revenue Overview</h4>
                    <div className="revenue-stats">
                      <div className="revenue-item">
                        <span>Completed Revenue:</span>
                        <span>${totalRevenue.toFixed(2)}</span>
                      </div>
                      <div className="revenue-item">
                        <span>Pending Revenue:</span>
                        <span>${pendingRevenue.toFixed(2)}</span>
                      </div>
                      <div className="revenue-item">
                        <span>Average Appointment:</span>
                        <span>${appointments.length > 0 ? (totalRevenue / appointments.filter(apt => apt.status === 'completed').length || 0).toFixed(2) : '0.00'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="analytics-card">
                    <h4>Appointment Status</h4>
                    <div className="status-breakdown">
                      {['pending', 'confirmed', 'completed', 'cancelled'].map(status => {
                        const count = appointments.filter(apt => apt.status === status).length;
                        const percentage = appointments.length > 0 ? (count / appointments.length * 100).toFixed(1) : '0';
                        return (
                          <div key={status} className="status-item">
                            <span className="status-label" style={{ color: getStatusColor(status) }}>
                              {status.charAt(0).toUpperCase() + status.slice(1)}:
                            </span>
                            <span>{count} ({percentage}%)</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div className="analytics-card">
                    <h4>Popular Services</h4>
                    <div className="services-breakdown">
                      {Object.entries(
                        appointments.reduce((acc, apt) => {
                          acc[apt.serviceName] = (acc[apt.serviceName] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>)
                      ).sort(([,a], [,b]) => b - a).slice(0, 5).map(([service, count]) => (
                        <div key={service} className="service-item">
                          <span className="service-name">{service}</span>
                          <span>{count} bookings</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
