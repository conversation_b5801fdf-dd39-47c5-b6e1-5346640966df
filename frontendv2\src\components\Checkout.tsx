import { useState } from 'react';

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
  customerInfo?: any;
}

interface CheckoutProps {
  booking: BookingState;
  onBack: () => void;
  onComplete: () => void;
}

export default function Checkout({ booking, onBack, onComplete }: CheckoutProps) {
  const [couponCode, setCouponCode] = useState('');
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('deposit'); // 'total' or 'deposit'
  const [paymentProof, setPaymentProof] = useState<File | null>(null);
  const [showUploadSection, setShowUploadSection] = useState(false);

  const calculateSubtotal = () => {
    const servicePrice = booking.selectedService ? parseFloat(booking.selectedService.price) : 0;
    const addOnsPrice = booking.selectedAddOns.reduce((total, addOn) => total + addOn.price, 0);
    return servicePrice + addOnsPrice;
  };

  const subtotal = calculateSubtotal();
  const depositAmount = 30.00;
  const balanceDue = subtotal - depositAmount;

  const handlePaymentMethodChange = (method: string) => {
    setPaymentMethod(method);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setPaymentProof(file);
    }
  };

  const handleContinuePayment = () => {
    // Here you would typically integrate with a payment processor
    // For now, we'll just complete the booking
    onComplete();
  };

  const handleUploadPayment = () => {
    if (paymentProof) {
      // Here you would upload the payment proof to your server
      console.log('Payment proof uploaded:', paymentProof.name);
      alert('Payment proof uploaded successfully! Your booking will be confirmed once payment is verified.');
      onComplete();
    } else {
      alert('Please select a payment proof file to upload.');
    }
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="checkout-container">
          <div className="checkout-header">
            <button className="back-button" onClick={onBack}>
              ← YOUR INFORMATION
            </button>
            <h2 className="checkout-title">Checkout</h2>
          </div>

          <div className="checkout-content">
            {/* Payment Section */}
            <div className="payment-section">
              <h3>PAYMENT</h3>
              
              <div className="payment-info">
                <h4>Payment information</h4>

                <div className="payment-options-section">
                  <div className="payment-method-card">
                    <div className="payment-method-info">
                      <span>💳 Online Payment</span>
                      <p className="payment-note">Secure payment processing</p>
                    </div>
                    <button className="payment-button" onClick={handleContinuePayment}>
                      CONTINUE WITH ONLINE PAYMENT
                    </button>
                  </div>

                  <div className="payment-divider">
                    <span>OR</span>
                  </div>

                  <div className="payment-method-card">
                    <div className="payment-method-info">
                      <span>📄 Upload Payment Proof</span>
                      <p className="payment-note">Upload screenshot or receipt of your payment</p>
                    </div>

                    <div className="upload-section">
                      <div className="file-upload-wrapper">
                        <input
                          type="file"
                          id="payment-proof"
                          accept="image/*,.pdf"
                          onChange={handleFileUpload}
                          className="file-input"
                        />
                        <label htmlFor="payment-proof" className="file-upload-label">
                          {paymentProof ? paymentProof.name : 'Choose File'}
                        </label>
                      </div>

                      <button
                        className="payment-button upload-button"
                        onClick={handleUploadPayment}
                        disabled={!paymentProof}
                      >
                        UPLOAD PAYMENT PROOF
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="order-summary">
              <h4>Order summary</h4>
              
              <div className="order-details">
                <div className="service-line">
                  <span className="service-name">{booking.selectedService?.name}</span>
                  <span className="service-price">${booking.selectedService?.price}</span>
                </div>
                
                <div className="service-date">
                  August 19th, 2025 at {booking.selectedTime} CDT
                </div>

                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    <span>+ {addOn.name}</span>
                    <span>${addOn.price.toFixed(2)}</span>
                  </div>
                ))}

                <div className="coupon-section">
                  <button 
                    className="coupon-toggle"
                    onClick={() => setShowCouponInput(!showCouponInput)}
                  >
                    Package, gift, or coupon code +
                  </button>
                  
                  {showCouponInput && (
                    <div className="coupon-input">
                      <input
                        type="text"
                        placeholder="Enter code"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                      />
                      <button>Apply</button>
                    </div>
                  )}
                </div>

                <div className="pricing-breakdown">
                  <div className="subtotal-line">
                    <span>Subtotal</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>

                  <div className="payment-options">
                    <div className="payment-option">
                      <input
                        type="radio"
                        id="total-payment"
                        name="payment-method"
                        value="total"
                        checked={paymentMethod === 'total'}
                        onChange={() => handlePaymentMethodChange('total')}
                      />
                      <label htmlFor="total-payment">
                        <span className="option-label">Total due</span>
                        <span className="option-price">${subtotal.toFixed(2)}</span>
                      </label>
                    </div>

                    <div className="payment-option">
                      <input
                        type="radio"
                        id="deposit-payment"
                        name="payment-method"
                        value="deposit"
                        checked={paymentMethod === 'deposit'}
                        onChange={() => handlePaymentMethodChange('deposit')}
                      />
                      <label htmlFor="deposit-payment">
                        <span className="option-label">Deposit due</span>
                        <span className="option-price">${depositAmount.toFixed(2)}</span>
                      </label>
                    </div>
                  </div>

                  {paymentMethod === 'deposit' && (
                    <div className="balance-due">
                      <span>Balance due</span>
                      <span>${balanceDue.toFixed(2)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
