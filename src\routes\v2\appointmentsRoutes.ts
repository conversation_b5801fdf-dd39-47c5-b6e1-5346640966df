import { Router } from 'express';
import { Request, Response } from 'express';
import { Appointment, Service, User } from '../../models';
import { authenticate, authorize } from '../../middleware/auth';
import { validate } from '../../middleware/validation';
import { createAppointmentValidation, mongoIdValidation } from '../../utils/validation';
import { sendSuccess, sendError, sendCreated } from '../../utils/response';
import { AuthenticatedRequest } from '../../types';

const router = Router();

// GET /api/v2/appointments/availability - Get available time slots
router.get('/availability', async (req: Request, res: Response) => {
  try {
    const { date, serviceId } = req.query;
    
    if (!date) {
      sendError(res, 'Date is required', undefined, 400);
      return;
    }
    
    const selectedDate = new Date(date as string);
    const startOfDay = new Date(selectedDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(selectedDate);
    endOfDay.setHours(23, 59, 59, 999);
    
    // Get existing appointments for the date
    const existingAppointments = await Appointment.find({
      date: {
        $gte: startOfDay,
        $lte: endOfDay
      },
      status: { $in: ['pending', 'confirmed'] }
    }).select('time');
    
    const bookedTimes = existingAppointments.map(apt => apt.time);
    
    // Generate available time slots (9 AM to 6 PM)
    const availableSlots = [];
    for (let hour = 9; hour <= 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        if (!bookedTimes.includes(timeSlot)) {
          availableSlots.push(timeSlot);
        }
      }
    }
    
    sendSuccess(res, 'Available time slots retrieved successfully', {
      date: date,
      availableSlots: availableSlots
    });
  } catch (error) {
    console.error('Get availability error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/appointments - Create new appointment
router.post('/', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { serviceId, date, time, customerInfo, addOns = [] } = req.body;

    // Validate required fields
    if (!serviceId || !date || !time || !customerInfo) {
      sendError(res, 'Service, date, time, and customer info are required', undefined, 400);
      return;
    }

    // Check if service exists
    const service = await Service.findById(serviceId);
    if (!service || !service.isActive) {
      sendError(res, 'Service not found or not available', undefined, 404);
      return;
    }

    // Check if time slot is available
    const appointmentDate = new Date(date);
    const existingAppointment = await Appointment.findOne({
      date: appointmentDate,
      time: time,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingAppointment) {
      sendError(res, 'Time slot is already booked', undefined, 409);
      return;
    }

    // Calculate total price (for response only, not stored in appointment)
    let totalPrice = service.price;
    const addOnTotal = addOns.reduce((sum: number, addOn: any) => sum + (addOn.price || 0), 0);
    totalPrice += addOnTotal;

    // Create appointment
    const appointment = await Appointment.create({
      user: req.user._id,
      service: serviceId,
      date: appointmentDate,
      time: time,
      status: 'pending',
      type: 'service',
      customerInfo: {
        name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        email: customerInfo.email,
        phone: customerInfo.phone
      },
      message: req.body.notes || ''
    });

    // Populate service data for response
    await appointment.populate('service', 'name price duration category');

    const responseData = {
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      servicePrice: (appointment.service as any).price,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalPrice: totalPrice,
      addOns: addOns,
      notes: appointment.message,
      createdAt: appointment.createdAt
    };

    sendCreated(res, 'Appointment created successfully', responseData);
  } catch (error) {
    console.error('Create appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/appointments/my - Get user's appointments
router.get('/my', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const appointments = await Appointment.find({ user: req.user._id })
      .populate('service', 'name price duration category')
      .sort({ date: -1, time: -1 });

    const formattedAppointments = appointments.map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      servicePrice: (appointment.service as any).price,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalPrice: (appointment.service as any).price, // Use service price as total
      notes: appointment.message,
      createdAt: appointment.createdAt
    }));

    sendSuccess(res, 'User appointments retrieved successfully', formattedAppointments);
  } catch (error) {
    console.error('Get user appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/appointments/:id - Update appointment
router.put('/:id', authenticate, validate(mongoIdValidation()), async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { id } = req.params;
    const { date, time, customerInfo, notes } = req.body;

    const appointment = await Appointment.findOne({
      _id: id,
      user: req.user._id
    });

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    // Only allow updates to pending appointments
    if (appointment.status !== 'pending') {
      sendError(res, 'Cannot update confirmed or completed appointments', undefined, 400);
      return;
    }

    // Update fields
    if (date) appointment.date = new Date(date);
    if (time) appointment.time = time;
    if (customerInfo) {
      appointment.customerInfo = {
        name: customerInfo.firstName && customerInfo.lastName
          ? `${customerInfo.firstName} ${customerInfo.lastName}`
          : appointment.customerInfo.name,
        email: customerInfo.email || appointment.customerInfo.email,
        phone: customerInfo.phone || appointment.customerInfo.phone
      };
    }
    if (notes !== undefined) appointment.message = notes;

    await appointment.save();
    await appointment.populate('service', 'name price duration category');

    const responseData = {
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      servicePrice: (appointment.service as any).price,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalPrice: (appointment.service as any).price,
      notes: appointment.message,
      updatedAt: appointment.updatedAt
    };

    sendSuccess(res, 'Appointment updated successfully', responseData);
  } catch (error) {
    console.error('Update appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// DELETE /api/v2/appointments/:id - Cancel appointment
router.delete('/:id', authenticate, validate(mongoIdValidation()), async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { id } = req.params;

    const appointment = await Appointment.findOne({
      _id: id,
      user: req.user._id
    });

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    appointment.status = 'cancelled';
    await appointment.save();

    sendSuccess(res, 'Appointment cancelled successfully', { id: appointment._id });
  } catch (error) {
    console.error('Cancel appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
