import { useState, useEffect } from 'react'
import './App.css'
import DateTimeSelection from './components/DateTimeSelection'
import BookingDetails from './components/BookingDetails'
import Checkout from './components/Checkout'
import Login from './components/Auth/Login'
import Signup from './components/Auth/Signup'
import UserDashboard from './components/Dashboard/UserDashboard'
import AdminDashboard from './components/Dashboard/AdminDashboard'
import {
  getCurrentUser,
  isAuthenticated,
  isAdmin,
  authAPI,
  type User
} from './utils/api'

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
  customerInfo?: any;
}

type AppView = 'home' | 'login' | 'signup' | 'dashboard' | 'booking';

// Service data based on the fetched API data
const serviceData = {
  selectAppointment: [
    {
      id: 35420512,
      name: "4-5 week Sisterlock Retightening",
      price: "165.00",
      description: "4-5 week Sisterlock maintenance"
    },
    {
      id: 63712209,
      name: "Spa Hair Wash",
      price: "35.00",
      description: "PLEASE NO EXTRA GUEST 🙃"
    },
    {
      id: 81516378,
      name: "**Los Angeles** Consultation",
      price: "60.00",
      description: "Required prior to booking a Sisterlocks or Microlocks Establishment. At the consultation, client hair history will be discussed. The Sisterlocks hair maintenance system and care will be discussed, including do's and don'ts. Client will ..."
    },
    {
      id: 40094534,
      name: "6-7 week Sisterlock Retightening",
      price: "185.00",
      description: "6-7 week Sisterlock maintenance\n\nPLEASE NO EXTRA GUEST 🙃"
    },
    {
      id: 40094560,
      name: "8 week Sisterlock Retightening",
      price: "210.00",
      description: "8 week Sisterlock maintenance\nPLEASE NO EXTRA GUEST 🙃"
    },
    {
      id: 38622888,
      name: "4-5 week Microlock retightening",
      price: "165.00",
      description: "4-5 week Microlock maintenance"
    },
    {
      id: 40094815,
      name: "6-7 week Microlock retightening",
      price: "185.00",
      description: "6-7 week Microlock maintenance"
    },
    {
      id: 40094841,
      name: "8 plus week Microlock retightening",
      price: "210.00",
      description: "8+ week Microlock maintenance"
    }
  ],
  consultation: [
    {
      id: 77567925,
      name: "Loc Extensions Consultation",
      price: "60.00",
      description: "Service includes options, Dates and Time discussions about Microloc extensions where human hair is added to your Locs at establishment day.\n\nNon refundable Fee"
    },
    {
      id: 81427530,
      name: "**Los Angeles** transfer client Consultation",
      price: "40.00",
      description: "This will be a virtual consultation.\nThis retie will be done on August 8th ONLY!"
    },
    {
      id: 31196455,
      name: "Sisterlocks/Microlock Consultation",
      price: "60.00",
      description: "Required prior to booking a Sisterlocks or Microlocks Establishment. At the consultation, client hair history will be discussed. The Sisterlocks hair maintenance system and care will be discussed, including do's and don'ts. Client will receive 4-8 sample locks. \nPlease ensure hair is freshly shampooed within 48 hours and free of oil, gel, conditioner, or grease. Hair should be in its natural state and not blow dried. Consultation fee is non refundable \n\nPLEASE NO EXTRA GUEST 🙃"
    },
    {
      id: 35811238,
      name: "Microlocks Transfer Client Consultation",
      price: "60.00",
      description: "This is a consultation ONLY not a guarantee of service. Hair must clean, and free of product no less than 24 hours prior to arrival for this service.\n\n**Please Note your locks will be maintained with my trained assistant, under my guidance."
    }
  ],
  classes: [
    {
      id: 69740982,
      name: "Coaching Calls",
      price: "100.00",
      description: "Whether you're new to the industry or an experienced Microloc Specialist seeking support, building a successful business can be challenging. Struggling with marketing, client retention, policies, social media, or pricing?\n\nBook a coaching call today ! \n\nCalls can be broken up into 2 sessions"
    },
    {
      id: 69741001,
      name: "Coaching Calls",
      price: "175.00",
      description: "Whether you're new to the industry or an experienced Microloc Specialist seeking support, building a successful business can be challenging. Struggling with marketing, client retention, policies, social media, or pricing?\n\nBook a coaching call today.\n\nCalls can be broken up into sessions !"
    },
    {
      id: 35420961,
      name: "Microlocks Virtual class",
      price: "950.00",
      description: "Microlocks Virtual class\nThis 5-part class will teach you everything you need to know about Microlocks, from installation to running your business.\nThis class will be held on March 22th and 23rd \nFrom 9am-4pm cst\n\n*** No refunds***please be prepared with Mannequin Head (kinky curly),combs and hair"
    }
  ],
  hairSpaDetox: [
    {
      id: 63712297,
      name: "Apple Cider Vinegar Detox",
      price: "55.00",
      description: ""
    },
    {
      id: 64355523,
      name: "Citrus Detox",
      price: "65.00",
      description: ""
    }
  ],
  lockRepair: [
    {
      id: 51339805,
      name: "Lock repairs",
      price: "50.00",
      description: "Professional lock repair service. Minimal 2hr Session\nPLEASE NO EXTRA GUEST 🙃"
    }
  ],
  traditional: [
    {
      id: 68701765,
      name: "Wash+retwist+style",
      price: "175.00",
      description: "PLEASE NO EXTRA GUEST 🙃"
    },
    {
      id: 68701778,
      name: "Kids retwist",
      price: "70.00",
      description: ""
    }
  ]
}



function App() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [currentView, setCurrentView] = useState<AppView>('home');
  const [showAllServices, setShowAllServices] = useState(false);
  const [booking, setBooking] = useState<BookingState>({
    selectedService: null,
    selectedAddOns: [],
    selectedDate: '',
    selectedTime: '',
    step: 'services'
  });

  // Check authentication on mount
  useEffect(() => {
    const user = getCurrentUser();
    if (user && isAuthenticated()) {
      setCurrentUser(user);
      setCurrentView('dashboard');
    }

    // Load booking state from URL parameters
    const params = new URLSearchParams(window.location.search);
    const step = params.get('step') as BookingState['step'] || 'services';
    const serviceId = params.get('service');
    const addonsParam = params.get('addons');
    const date = params.get('date');
    const time = params.get('time');

    if (step !== 'services') {
      let selectedService = null;
      if (serviceId) {
        // Find service by ID from all service categories
        const allServices = [
          ...serviceData.selectAppointment,
          ...serviceData.classes,
          ...serviceData.hairSpaDetox,
          ...serviceData.lockRepair,
          ...serviceData.traditional
        ];
        selectedService = allServices.find(service => service.id.toString() === serviceId);
      }

      let selectedAddOns: AddOnService[] = [];
      if (addonsParam) {
        const addonIds = addonsParam.split(',');
        selectedAddOns = addOnServices.filter(addon => addonIds.includes(addon.id));
      }

      setBooking({
        selectedService,
        selectedAddOns,
        selectedDate: date || '',
        selectedTime: time ? decodeURIComponent(time) : '',
        step
      });

      if (user) {
        setCurrentView('booking');
      }
    }
  }, []);

  const addOnServices: AddOnService[] = [
    { id: 'apple-cider', name: 'Apple Cider Vinegar Detox', duration: 30, price: 55 },
    { id: 'rose-water', name: 'Rose water Detox', duration: 30, price: 55 },
    { id: 'scalp-scrub', name: 'Scalp scrub', duration: 10, price: 30 },
    { id: 'spa-wash', name: 'Spa Hair Wash', duration: 15, price: 35 }
  ];

  // Authentication handlers
  const handleLogin = (user: User) => {
    setCurrentUser(user);

    // If user was in the middle of booking, continue the booking flow
    if (booking.selectedService) {
      setCurrentView('booking');
    } else {
      setCurrentView('dashboard');
    }
  };

  const handleSignup = (user: User) => {
    setCurrentUser(user);

    // If user was in the middle of booking, continue the booking flow
    if (booking.selectedService) {
      setCurrentView('booking');
    } else {
      setCurrentView('dashboard');
    }
  };

  const handleLogout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    }

    setCurrentUser(null);
    setCurrentView('home');
    setBooking({
      selectedService: null,
      selectedAddOns: [],
      selectedDate: '',
      selectedTime: '',
      step: 'services'
    });
    // Clear URL parameters
    window.history.pushState({}, '', window.location.pathname);
  };

  const handleStartBooking = () => {
    if (currentUser) {
      setCurrentView('booking');
    } else {
      setCurrentView('login');
    }
  };

  const handleBookService = (service: any) => {
    // Allow booking for all users, no login required initially
    const newBooking = {
      ...booking,
      selectedService: service,
      step: 'datetime' as const
    };
    setBooking(newBooking);
    setCurrentView('booking');
    updateURL(newBooking);
  };

  const handleAddOnToggle = (addOn: AddOnService) => {
    const isSelected = booking.selectedAddOns.find(item => item.id === addOn.id);
    let newBooking;

    if (isSelected) {
      newBooking = {
        ...booking,
        selectedAddOns: booking.selectedAddOns.filter(item => item.id !== addOn.id)
      };
    } else {
      newBooking = {
        ...booking,
        selectedAddOns: [...booking.selectedAddOns, addOn]
      };
    }

    setBooking(newBooking);
    updateURL(newBooking);
  };

  const handleDateTimeSelect = (date: string, time: string) => {
    const newBooking = {
      ...booking,
      selectedDate: date,
      selectedTime: time,
      step: 'details' as const
    };
    setBooking(newBooking);
    updateURL(newBooking);
  };

  const handleBackToServices = () => {
    const newBooking = {
      ...booking,
      step: 'services' as const
    };
    setBooking(newBooking);
    setCurrentView('home');
    updateURL(newBooking);
  };

  const handleCustomerInfoSubmit = (customerInfo: any) => {
    const newBooking = {
      ...booking,
      customerInfo,
      step: 'checkout' as const
    };
    setBooking(newBooking);
    updateURL(newBooking);
  };

  const handleCheckoutComplete = async () => {
    if (!currentUser || !booking.selectedService || !booking.customerInfo) {
      console.error('Missing required booking information');
      return;
    }

    try {
      // TODO: Implement appointment creation API call
      const totalPrice = parseFloat(booking.selectedService.price) +
        booking.selectedAddOns.reduce((sum, addon) => sum + addon.price, 0);

      // For now, just show success message
      console.log('Appointment data:', {
        serviceId: booking.selectedService.id.toString(),
        serviceName: booking.selectedService.name,
        servicePrice: parseFloat(booking.selectedService.price),
        addOns: booking.selectedAddOns,
        date: booking.selectedDate,
        time: booking.selectedTime,
        customerInfo: booking.customerInfo,
        totalPrice
      });

      // Reset booking state and redirect to dashboard
      setBooking({
        selectedService: null,
        selectedAddOns: [],
        selectedDate: '',
        selectedTime: '',
        step: 'services'
      });
      setCurrentView('dashboard');

      // Clear URL parameters
      window.history.pushState({}, '', window.location.pathname);

      // Show success message
      alert('Appointment booking submitted! We will contact you to confirm your appointment.');
    } catch (error) {
      console.error('Appointment booking error:', error);
      alert('Failed to book appointment. Please try again.');
    }
  };

  const updateURL = (currentBooking: BookingState) => {
    const params = new URLSearchParams();

    if (currentBooking.selectedService) {
      params.set('service', currentBooking.selectedService.id.toString());
    }

    if (currentBooking.selectedAddOns.length > 0) {
      params.set('addons', currentBooking.selectedAddOns.map(addon => addon.id).join(','));
    }

    if (currentBooking.selectedDate) {
      params.set('date', currentBooking.selectedDate);
    }

    if (currentBooking.selectedTime) {
      params.set('time', encodeURIComponent(currentBooking.selectedTime));
    }

    params.set('step', currentBooking.step);

    const newURL = `${window.location.pathname}?${params.toString()}`;
    window.history.pushState({}, '', newURL);
  };



  // Render different views based on current state
  if (currentView === 'login') {
    return <Login onLogin={handleLogin} onSwitchToSignup={() => setCurrentView('signup')} />;
  }

  if (currentView === 'signup') {
    return <Signup onSignup={handleSignup} onSwitchToLogin={() => setCurrentView('login')} />;
  }

  if (currentView === 'dashboard') {
    if (currentUser?.role === 'admin') {
      return <AdminDashboard onLogout={handleLogout} />;
    } else {
      return <UserDashboard onLogout={handleLogout} onBookNew={handleStartBooking} />;
    }
  }

  if (currentView === 'booking') {
    if (booking.step === 'datetime') {
      return <DateTimeSelection booking={booking} onBack={handleBackToServices} onSelect={handleDateTimeSelect} addOnServices={addOnServices} onAddOnToggle={handleAddOnToggle} />;
    }

    if (booking.step === 'details') {
      return <BookingDetails booking={booking} onBack={() => setBooking({...booking, step: 'datetime'})} onContinue={handleCustomerInfoSubmit} />;
    }

    if (booking.step === 'checkout') {
      return <Checkout booking={booking} onBack={() => setBooking({...booking, step: 'details'})} onComplete={handleCheckoutComplete} />;
    }
  }

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            {currentUser ? (
              <>
                <button
                  className="auth-link"
                  onClick={() => setCurrentView('dashboard')}
                >
                  DASHBOARD
                </button>
                <button className="auth-link" onClick={handleLogout}>
                  LOGOUT
                </button>
              </>
            ) : (
              <>
                <button
                  className="auth-link"
                  onClick={() => setCurrentView('signup')}
                >
                  SIGN UP
                </button>
                <button
                  className="auth-link"
                  onClick={() => setCurrentView('login')}
                >
                  LOG IN
                </button>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-content">
        {/* Profile Section */}
        <div className="profile-section">
          <div className="profile-image">
            <div className="profile-avatar">
              <div className="avatar-placeholder">DSB</div>
            </div>
          </div>
          <h1 className="business-name">dammyspicybeauty</h1>
        </div>

        {/* Hero Banner */}
        <div className="hero-banner">
          <div className="hero-content">
            <div className="hero-text">
              <h2 className="hero-title">dammy</h2>
              <p className="hero-subtitle">spicybeuty</p>
              <p className="location">PRINCETON/MCKINNEY, TX</p>
              <p className="welcome-text">Welcome to my booking site!</p>
            </div>
            <div className="hero-image">
              <div className="stylist-image">
                <div className="stylist-placeholder">
                  <div className="stylist-silhouette"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Service Information Sections */}
        <div className="info-sections">
          {/* About Me Section */}
          <div className="info-card about-me-card">
            <h3 className="info-title">About Me</h3>
            <div className="info-content">
              <p>Thank you for choosing dammyspicybeauty.</p>
              <p>My name is Dammy I'm an approved beauty specialist. I specialize in hair care and beauty treatments. I'm located in the Princeton/McKinney TX area. My main objective is to offer women the beauty freedom we all desire.</p>
              <p>I look forward to assisting you with your lock journey!</p>
            </div>
          </div>

          {/* Sisterlocks Section */}
          <div className="info-card sisterlocks-card">
            <h3 className="info-title">Sisterlocks</h3>
            <div className="info-content">
              <p>A non refundable deposit is due to secure all Sisterlocks establishment appointments. This must be paid at the time of scheduling. Full final payment is due day one of establishment.</p>
              <p className="package-title">The Sisterlock Package consists of the following:</p>
              <ul className="package-list">
                <li>CONSULTATION</li>
                <li>ESTABLISHMENT</li>
              </ul>
              <p>Establishment usually take 2-3 days depending on Hair, Lock size, Lock Method, Hair Length, Density and Head size</p>
            </div>
          </div>

          {/* Microlocks Section */}
          <div className="info-card microlocks-card">
            <h3 className="info-title">Microlocks</h3>
            <div className="info-content">
              <p>A non refundable deposit is due to secure all Microlock establishment appointments. This must be paid at the time of scheduling. Full final payment is due day one of establishment.</p>
              <p className="methods-title">3 STARTING METHODS TO CHOOSE FROM:</p>
              <ul className="methods-list">
                <li>INTERLOCKS</li>
                <li>2-STRAND TWIST</li>
                <li>BRAID LOCS</li>
              </ul>
              <p>Establishment usually take 1-2 days depending on Hair, Lock size, Lock Method, Hair Length, Density and Head size</p>
            </div>
          </div>

          {/* Price List Section */}
          <div className="info-card price-list-card">
            <h3 className="info-title">Price list</h3>
            <div className="info-content">
              <div className="price-item">
                <h4>Sisterlocks.......​$1300 (starting price)</h4>
              </div>
              <div className="price-item">
                <h4>Microlocks......</h4>
                <p>Smedium $950 (starting price)</p>
                <p>Small $1150 (starting price)</p>
              </div>
              <p className="price-note">*Hair must be shampooed, clean, and free of product no less than 24 hours prior to arrival for this service. If your hair is not washed your appointment can be cancelled and 50% OF SERVICE WILL BE DUE.*</p>
            </div>
          </div>

          {/* Reties Price List Section */}
          <div className="info-card price-list-card">
            <h3 className="info-title">Price list</h3>
            <div className="info-content">
              <h4 className="reties-title">Reties</h4>
              <div className="reties-pricing">
                <div className="retie-item">
                  <span className="retie-duration">4-5 weeks</span>
                  <span className="retie-dots">.............</span>
                  <span className="retie-price">$165</span>
                </div>
                <div className="retie-item">
                  <span className="retie-duration">6-7 weeks</span>
                  <span className="retie-dots">.............</span>
                  <span className="retie-price">$185</span>
                </div>
                <div className="retie-item">
                  <span className="retie-duration">8 weeks+</span>
                  <span className="retie-dots">.............</span>
                  <span className="retie-price">$200</span>
                </div>
              </div>
              <p className="retie-note">Up to 4 hours Re-tightening at the root. ($40 per additional hour).</p>
              <p className="price-note">*Hair must be shampooed, clean, and free of product no less than 24 hours prior to arrival for this service. If your hair is not washed your appointment can be cancelled and 50% OF SERVICE WILL BE DUE.*</p>
            </div>
          </div>
        </div>

        {/* Policies Section */}
        <div className="policies-section">
          <div className="policies-card">
            <div className="policies-header">
              <h1 className="business-name-policies">DammySpicy Beauty LLC</h1>
              <p className="business-tagline">Where Beauty Meets Intention</p>
              <div className="policies-divider"></div>
              <h2 className="policies-title">Policies & Terms of Service</h2>
            </div>

            <div className="policies-content">
              <div className="policy-item">
                <div className="policy-icon">📌</div>
                <div className="policy-details">
                  <h4 className="policy-heading">TO BOOK</h4>
                  <p className="policy-text">A non-refundable deposit is required to secure any service. This amount goes toward your total service.</p>
                </div>
              </div>

              <div className="policy-item">
                <div className="policy-icon">⏰</div>
                <div className="policy-details">
                  <h4 className="policy-heading">REMAINING BALANCE</h4>
                  <p className="policy-text">CASH ONLY is accepted at time of your appointment. No digital payments will be accepted</p>
                </div>
              </div>

              <div className="policy-item">
                <div className="policy-icon">🚫</div>
                <div className="policy-details">
                  <h4 className="policy-heading">RESCHEDULING</h4>
                  <p className="policy-text">Same-day reschedule - loss of deposit</p>
                </div>
              </div>

              <div className="policy-item">
                <div className="policy-icon">❌</div>
                <div className="policy-details">
                  <h4 className="policy-heading">CANCELLATIONS</h4>
                  <p className="policy-text">All services must be cancelled at least 24 hours before your appointment.</p>
                </div>
              </div>

              <div className="policy-item">
                <div className="policy-icon">🚫</div>
                <div className="policy-details">
                  <h4 className="policy-heading">NO REFUNDS</h4>
                  <p className="policy-text">All services are final once completed.</p>
                </div>
              </div>

              <div className="policy-item">
                <div className="policy-details payment-options">
                  <h4 className="policy-heading">DEPOSIT PAYMENT OPTIONS</h4>
                  <div className="payment-methods">
                    <div className="payment-method">
                      <h5>Cash App:</h5>
                      <p>(*************</p>
                      <p>(*************</p>
                    </div>
                    <div className="payment-method">
                      <h5>Zelle:</h5>
                      <p>DammySpicyBeauty LLC</p>
                      <p>(*************</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="policies-footer">
              <p>Thank you for choosing DammySpicy Beauty LLC. We appreciate your business and look forward to serving you with love and excellence!</p>
            </div>
          </div>
        </div>

        {/* Important Notice Section */}
        <div className="important-notice-section">
          <div className="important-notice-card">
            <h3 className="notice-title">**Important Notice* Please Read**</h3>
            <div className="notice-content">
              <p>• If you are not a current client, <strong>please DO NOT book an appointment</strong>. Any such <strong>appointments will be canceled without a refund</strong>, and this policy will be enforced in any dispute.</p>

              <p>• If you have not <strong>been serviced with dammyspicybeauty or it has been more than 90 days</strong> since your last visit, you must book a consultation first. If no consultation slots are available, it means I am not accepting new or transfer clients at this time.</p>

              <p><strong>For current clients:</strong> Please book your correct appointment slot (4week-8weeks plus) or your appointment will be canceled and your deposit will be forfeited without exception.</p>

              <p>• There is a $30 deposit to book your appointment. <strong>Your deposit is not refundable</strong> and transferable once if your appointment is rescheduled within 48 hours.</p>
            </div>
          </div>
        </div>

        {/* Social Media Section */}
        <div className="social-section">
          <div className="social-card">
            <div className="social-content">
              <div className="social-text">
                <h3 className="social-title">thank you for choosing us</h3>
                <div className="social-script">tag us in your selfies</div>
                <div className="social-hashtag">#dammyspicybeauty</div>
                <div className="social-handle">@dammyspicybeauty</div>
                <div className="social-cta">PLEASE CHOOSE THE SERVICES BELOW</div>
                <div className="social-arrow">⬇</div>
              </div>
              <div className="social-phone">
                <div className="phone-mockup">
                  <div className="phone-screen">
                    <div className="hair-image-placeholder">
                      <div className="hair-texture"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Section */}
        <div className="booking-section">
          <div className="booking-header">
            <h2 className="booking-title">📅 Select Appointment</h2>
          </div>

          {/* Select Appointment Services */}
          <div className="service-category">
            {serviceData.selectAppointment.slice(0, showAllServices ? serviceData.selectAppointment.length : 3).map((service) => (
              <div key={service.id} className="service-item">
                <div className="service-info">
                  <div className="service-header">
                    <h3 className="service-name">{service.name}</h3>
                    <p className="service-price">${service.price}</p>
                  </div>
                  {service.description && (
                    <p className="service-description">{service.description}</p>
                  )}
                </div>
                <button className="book-button" onClick={() => handleBookService(service)}>BOOK</button>
              </div>
            ))}

            {!showAllServices && serviceData.selectAppointment.length > 3 && (
              <button
                className="show-all-button"
                onClick={() => setShowAllServices(true)}
              >
                SHOW ALL
              </button>
            )}
          </div>

          {/* Consultation Services */}
          <div className="service-category">
            <h3 className="category-title">Consultation</h3>
            {serviceData.consultation.map((service) => (
              <div key={service.id} className="service-item">
                <div className="service-info">
                  <div className="service-header">
                    <h3 className="service-name">{service.name}</h3>
                    <p className="service-price">${service.price}</p>
                  </div>
                  {service.description && (
                    <p className="service-description">{service.description}</p>
                  )}
                </div>
                <button className="book-button" onClick={() => handleBookService(service)}>BOOK</button>
              </div>
            ))}
          </div>

          {/* Classes */}
          <div className="service-category">
            <h3 className="category-title">Classes</h3>
            {serviceData.classes.map((service) => (
              <div key={service.id} className="service-item">
                <div className="service-info">
                  <div className="service-header">
                    <h3 className="service-name">{service.name}</h3>
                    <p className="service-price">${service.price}</p>
                  </div>
                  {service.description && (
                    <p className="service-description">{service.description}</p>
                  )}
                </div>
                <button className="book-button" onClick={() => handleBookService(service)}>BOOK</button>
              </div>
            ))}
          </div>

          {/* Hair Spa and Detox */}
          <div className="service-category">
            <h3 className="category-title">Hair Spa and Detox</h3>
            {serviceData.hairSpaDetox.map((service) => (
              <div key={service.id} className="service-item">
                <div className="service-info">
                  <div className="service-header">
                    <h3 className="service-name">{service.name}</h3>
                    <p className="service-price">${service.price}</p>
                  </div>
                  {service.description && (
                    <p className="service-description">{service.description}</p>
                  )}
                </div>
                <button className="book-button" onClick={() => handleBookService(service)}>BOOK</button>
              </div>
            ))}
          </div>

          {/* Lock repair/Take Down */}
          <div className="service-category">
            <h3 className="category-title">Lock repair/Take Down</h3>
            {serviceData.lockRepair.map((service) => (
              <div key={service.id} className="service-item">
                <div className="service-info">
                  <div className="service-header">
                    <h3 className="service-name">{service.name}</h3>
                    <p className="service-price">${service.price}</p>
                  </div>
                  {service.description && (
                    <p className="service-description">{service.description}</p>
                  )}
                </div>
                <button className="book-button" onClick={() => handleBookService(service)}>BOOK</button>
              </div>
            ))}
          </div>

          {/* Traditional */}
          <div className="service-category">
            <h3 className="category-title">Traditional</h3>
            {serviceData.traditional.map((service) => (
              <div key={service.id} className="service-item">
                <div className="service-info">
                  <div className="service-header">
                    <h3 className="service-name">{service.name}</h3>
                    <p className="service-price">${service.price}</p>
                  </div>
                  {service.description && (
                    <p className="service-description">{service.description}</p>
                  )}
                </div>
                <button className="book-button" onClick={() => handleBookService(service)}>BOOK</button>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
