import { useState } from 'react';

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
}

interface DateTimeSelectionProps {
  booking: BookingState;
  onBack: () => void;
  onSelect: (date: string, time: string) => void;
  addOnServices: AddOnService[];
  onAddOnToggle: (addOn: AddOnService) => void;
}

export default function DateTimeSelection({ 
  booking, 
  onBack, 
  onSelect, 
  addOnServices, 
  onAddOnToggle 
}: DateTimeSelectionProps) {
  const [selectedDate, setSelectedDate] = useState('');

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
  };

  const handleTimeSelect = (time: string) => {
    if (selectedDate) {
      onSelect(selectedDate, time);
    }
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="datetime-selection">
          <div className="datetime-header">
            <button className="back-button" onClick={onBack}>
              ← SELECT APPOINTMENT
            </button>
            <h2 className="datetime-title">Date & Time</h2>
          </div>

          {/* Appointment Summary */}
          <div className="appointment-summary">
            <h3>APPOINTMENT</h3>
            <div className="appointment-card">
              <div className="appointment-details">
                <h4>{booking.selectedService?.name}</h4>
                <p className="appointment-price">${booking.selectedService?.price}</p>
                
                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    + {addOn.name}, {addOn.duration} minutes @ ${addOn.price}
                  </div>
                ))}
                
                <p className="appointment-note">*EXISTING CLIENTS ONLY*</p>
              </div>
              <button className="close-button">×</button>
            </div>
          </div>

          {/* Add-on Services */}
          <div className="addon-section">
            <h3>ADD TO APPOINTMENT</h3>
            <div className="addon-grid">
              {addOnServices.map(addOn => (
                <div key={addOn.id} className="addon-item">
                  <input
                    type="checkbox"
                    id={addOn.id}
                    checked={booking.selectedAddOns.some(item => item.id === addOn.id)}
                    onChange={() => onAddOnToggle(addOn)}
                  />
                  <label htmlFor={addOn.id}>
                    {addOn.name}
                    <br />
                    + {addOn.duration} minutes @ ${addOn.price}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Calendar */}
          <div className="calendar-section">
            <div className="calendar-header">
              <button className="nav-button">←</button>
              <h3>August 2025</h3>
              <button className="nav-button">→</button>
            </div>
            
            <div className="calendar-grid">
              <div className="calendar-days">
                <div className="day-header">S</div>
                <div className="day-header">M</div>
                <div className="day-header">T</div>
                <div className="day-header">W</div>
                <div className="day-header">T</div>
                <div className="day-header">F</div>
                <div className="day-header">S</div>
              </div>
              
              <div className="calendar-dates">
                {/* Previous month dates */}
                <div className="date-cell prev-month">27</div>
                <div className="date-cell prev-month">28</div>
                <div className="date-cell prev-month">29</div>
                <div className="date-cell prev-month">30</div>
                <div className="date-cell prev-month">31</div>
                
                {/* Current month dates */}
                {Array.from({length: 31}, (_, i) => i + 1).map(date => {
                  const dateStr = `2025-08-${date.toString().padStart(2, '0')}`;
                  const isSelected = selectedDate === dateStr;
                  const isAvailable = date === 19; // Only 19th is available for demo
                  
                  return (
                    <div 
                      key={date}
                      className={`date-cell ${isSelected ? 'selected' : ''} ${isAvailable ? 'available' : ''}`}
                      onClick={() => isAvailable ? handleDateSelect(dateStr) : null}
                    >
                      {date}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Selected Date and Time */}
            {selectedDate && (
              <div className="time-selection">
                <div className="selected-date">
                  <h4>Tuesday, August 19</h4>
                  <p className="timezone">TIME ZONE: CENTRAL TIME (GMT-5:00)</p>
                </div>
                
                <div className="time-slots">
                  <button 
                    className="time-slot"
                    onClick={() => handleTimeSelect('7:00 AM')}
                  >
                    7:00 AM
                  </button>
                  <button 
                    className="time-slot"
                    onClick={() => handleTimeSelect('11:00 AM')}
                  >
                    11:00 AM
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
