import { useState } from 'react';

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
}

interface BookingDetailsProps {
  booking: BookingState;
  onBack: () => void;
  onContinue: (customerInfo: any) => void;
}

export default function BookingDetails({ booking, onBack, onContinue }: BookingDetailsProps) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = () => {
    onContinue(formData);
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="booking-details">
          {/* Appointment Summary */}
          <div className="appointment-summary">
            <div className="appointment-card">
              <div className="appointment-details">
                <h4>{booking.selectedService?.name}</h4>
                <p className="appointment-price">${booking.selectedService?.price}</p>
                <p className="appointment-datetime">
                  Tuesday, August 19th, 2025 at {booking.selectedTime} CDT
                </p>
                
                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    + {addOn.name}, {addOn.duration} minutes @ ${addOn.price}
                  </div>
                ))}
                
                <p className="appointment-note">*EXISTING CLIENTS ONLY*</p>
              </div>
              <button className="close-button">×</button>
            </div>
          </div>

          {/* Customer Information Form */}
          <div className="customer-info">
            <h3>YOUR INFORMATION</h3>
            
            <div className="form-group">
              <label htmlFor="firstName">FIRST NAME*</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="lastName">LAST NAME*</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="phone">PHONE*</label>
              <div className="phone-input">
                <span className="country-code">🇺🇸 +1</span>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="email">EMAIL*</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Add..."
                required
              />
              <small>Use a comma or press enter/return to add additional email addresses</small>
            </div>

            <button className="continue-button" onClick={handleSubmit}>
              CONTINUE TO PAYMENT
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
