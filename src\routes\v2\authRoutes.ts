import { Router } from 'express';
import { AuthController } from '../../controllers';
import { authenticate } from '../../middleware/auth';
import { validate } from '../../middleware/validation';
import {
  registerValidation,
  loginValidation,
  forgotPasswordValidation
} from '../../utils/validation';

const router = Router();

// POST /api/v2/auth/register
router.post(
  '/register',
  validate(registerValidation),
  AuthController.register
);

// POST /api/v2/auth/login
router.post(
  '/login',
  validate(loginValidation),
  AuthController.login
);

// POST /api/v2/auth/forgot-password
router.post(
  '/forgot-password',
  validate(forgotPasswordValidation),
  AuthController.forgotPassword
);

// POST /api/v2/auth/reset-password
router.post(
  '/reset-password',
  AuthController.resetPassword
);

// GET /api/v2/auth/verify
router.get(
  '/verify',
  authenticate,
  AuthController.verify
);

// POST /api/v2/auth/logout
router.post(
  '/logout',
  authenticate,
  AuthController.logout
);

// GET /api/v2/auth/me - Get current user info (simplified for frontend v2)
router.get(
  '/me',
  authenticate,
  (req: any, res) => {
    const user = req.user;
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    res.json({
      success: true,
      data: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        role: user.role,
        createdAt: user.createdAt
      }
    });
  }
);

export default router;
