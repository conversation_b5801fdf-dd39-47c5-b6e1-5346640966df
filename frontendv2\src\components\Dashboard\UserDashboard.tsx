import { useState, useEffect } from 'react';
import { getCurrentUser, getUserAppointments, logout, type Appointment } from '../../utils/auth';

interface UserDashboardProps {
  onLogout: () => void;
  onBookNew: () => void;
}

export default function UserDashboard({ onLogout, onBookNew }: UserDashboardProps) {
  const [user, setUser] = useState(getCurrentUser());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past' | 'profile'>('upcoming');

  useEffect(() => {
    if (user) {
      const userAppointments = getUserAppointments(user.id);
      setAppointments(userAppointments);
    }
  }, [user]);

  const handleLogout = () => {
    logout();
    onLogout();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return timeString;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const upcomingAppointments = appointments.filter(apt => 
    new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
  );

  const pastAppointments = appointments.filter(apt => 
    new Date(apt.date) < new Date() || apt.status === 'completed' || apt.status === 'cancelled'
  );

  if (!user) {
    return <div>Please log in to view your dashboard.</div>;
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-logo">
            <h1>dammyspicybeauty</h1>
          </div>
          <div className="dashboard-user-menu">
            <span className="welcome-text">Welcome, {user.firstName}!</span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-content">
          {/* Quick Actions */}
          <div className="quick-actions">
            <button className="action-button primary" onClick={onBookNew}>
              📅 Book New Appointment
            </button>
            <div className="stats-grid">
              <div className="stat-card">
                <h3>{upcomingAppointments.length}</h3>
                <p>Upcoming Appointments</p>
              </div>
              <div className="stat-card">
                <h3>{pastAppointments.length}</h3>
                <p>Past Appointments</p>
              </div>
              <div className="stat-card">
                <h3>${appointments.reduce((sum, apt) => sum + apt.totalPrice, 0).toFixed(2)}</h3>
                <p>Total Spent</p>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="dashboard-tabs">
            <button 
              className={`tab-button ${activeTab === 'upcoming' ? 'active' : ''}`}
              onClick={() => setActiveTab('upcoming')}
            >
              Upcoming Appointments ({upcomingAppointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'past' ? 'active' : ''}`}
              onClick={() => setActiveTab('past')}
            >
              Past Appointments ({pastAppointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'upcoming' && (
              <div className="appointments-list">
                {upcomingAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No upcoming appointments</h3>
                    <p>Book your next appointment to get started!</p>
                    <button className="action-button primary" onClick={onBookNew}>
                      Book Appointment
                    </button>
                  </div>
                ) : (
                  upcomingAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {formatTime(appointment.time)}</p>
                        <p><strong>Total:</strong> ${appointment.totalPrice.toFixed(2)}</p>
                        {appointment.addOns.length > 0 && (
                          <div className="add-ons">
                            <strong>Add-ons:</strong>
                            <ul>
                              {appointment.addOns.map(addon => (
                                <li key={addon.id}>{addon.name} (+${addon.price})</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {appointment.notes && (
                          <div className="appointment-notes">
                            <strong>Notes:</strong> {appointment.notes}
                          </div>
                        )}
                        {!appointment.depositPaid && (
                          <div className="payment-notice">
                            <strong>⚠️ Deposit Required:</strong> ${appointment.depositAmount.toFixed(2)}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'past' && (
              <div className="appointments-list">
                {pastAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No past appointments</h3>
                    <p>Your appointment history will appear here.</p>
                  </div>
                ) : (
                  pastAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card past">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {formatTime(appointment.time)}</p>
                        <p><strong>Total:</strong> ${appointment.totalPrice.toFixed(2)}</p>
                        {appointment.addOns.length > 0 && (
                          <div className="add-ons">
                            <strong>Add-ons:</strong>
                            <ul>
                              {appointment.addOns.map(addon => (
                                <li key={addon.id}>{addon.name} (+${addon.price})</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="profile-section">
                <div className="profile-card">
                  <h3>Personal Information</h3>
                  <div className="profile-info">
                    <div className="info-row">
                      <label>Name:</label>
                      <span>{user.firstName} {user.lastName}</span>
                    </div>
                    <div className="info-row">
                      <label>Email:</label>
                      <span>{user.email}</span>
                    </div>
                    <div className="info-row">
                      <label>Phone:</label>
                      <span>{user.phone}</span>
                    </div>
                    <div className="info-row">
                      <label>Member Since:</label>
                      <span>{new Date(user.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="info-row">
                      <label>Last Login:</label>
                      <span>{user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</span>
                    </div>
                  </div>
                  <button className="action-button secondary">
                    Edit Profile
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
